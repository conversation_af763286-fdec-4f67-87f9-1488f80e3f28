package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func (r *buyerRepositoryImpl) GetById(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.First(&buyer, id).Error
	return buyer, err
}

func (r *buyerRepositoryImpl) GetByIdWithPrefix(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.Table("buyer").Select(
		`buyer.*,
		u.username,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,username FROM users) AS u ON u.id = buyer.user_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer.nationality_id").
		First(&buyer, id).Error
	return buyer, err
}

func (r *buyerRepositoryImpl) GetAllByIds(ids []int) ([]*entity.Buyer, error) {
	var buyers []*entity.Buyer
	err := r.DB.Table("buyer").Select(
		`buyer.*,
		u.username,
		pa.description_th AS prefix_name_th,
		pa.description_en AS prefix_name_en,
		nationality.nationality_th AS nationality_th,
		nationality.nationality_en AS nationality_en`).
		Joins("LEFT JOIN (SELECT id,description_th,description_en FROM master_prefix_name) AS pa ON pa.id = buyer.prefix_name_id").
		Joins("LEFT JOIN (SELECT id,username FROM users) AS u ON u.id = buyer.user_id").
		Joins("LEFT JOIN (SELECT id,nationality_th,nationality_en FROM master_country) AS nationality ON nationality.id = buyer.nationality_id").
		Where("buyer.id IN ?", ids).Find(&buyers).Error
	return buyers, err
}

func (r *buyerRepositoryImpl) FindBuyerWithFilter(req dto.MemberSearchReqDto) ([]entity.Buyer, error) {
	var results []entity.Buyer

	query := r.DB.Model(&entity.Buyer{}).Select("buyer.*,CONCAT(first_name, middle_name, last_name) AS name,master_prefix_name.description_th AS prefix").
		
		Joins("LEFT JOIN master_prefix_name ON master_prefix_name.id = buyer.prefix_name_id").
		Joins("LEFT JOIN users u ON buyer.user_id = u.id").
		Preload("CustomerGroupForJoin")

	query = query.Where("u.manager_id is null")

	if req.CustomerGroupId != nil {
		query = query.Where("customer_group_id = ?", req.CustomerGroupId)
	}

	if req.Username != nil {
		query = query.Where("username LIKE ?", fmt.Sprintf("%%%s%%", *req.Username))
	}

	if req.BidderId != nil {
		query = query.Where("bidder_id LIKE ?", fmt.Sprintf("%%%s%%", *req.BidderId))
	}

	if req.Name != nil {
		query = query.Where("CONCAT(first_name, middle_name, last_name) LIKE ?", fmt.Sprintf("%%%s%%", *req.Name))
	}

	if req.TaxId != nil {
		query = query.Where("identification_number LIKE ?", fmt.Sprintf("%%%s%%", *req.TaxId))
	}

	sortFieldMap := map[string]string{
		"taxId":         "buyer.identification_number",
		"customerGroup": "buyer.customer_group_id",
		"name":          "CONCAT(buyer.first_name, buyer.middle_name, buyer.last_name)",
		"username":      "u.username",
	}

	if req.SortBy != "" {
		if req.SortBy == "isActive" {
			orderClause := fmt.Sprintf(`
				CASE
					WHEN buyer.is_blacklist = true THEN 1
					WHEN buyer.is_block = true THEN 2
					ELSE 3
				END %s`, req.SortOrder)
			query = query.Order(orderClause)
		} else if dbField, ok := sortFieldMap[req.SortBy]; ok {
			query = query.Order(fmt.Sprintf("%s %s", dbField, req.SortOrder))
		} else {
			snakeSort := util.CamelToSnake(req.SortBy)
			query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "buyer")
		}
	}
	query.Order("buyer.created_date desc, buyer.updated_date desc")

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerRepositoryImpl) CountBuyerWithFilter(req dto.MemberSearchReqDto) (int64, error) {
	var count int64

	query := r.DB.Model(&entity.Buyer{}).Select("buyer.*,CONCAT(first_name, middle_name, last_name) AS name,master_prefix_name.description_th AS prefix").
		Joins("LEFT JOIN master_prefix_name ON master_prefix_name.id = buyer.prefix_name_id").
		Joins("LEFT JOIN users u ON buyer.user_id = u.id").
		Preload("CustomerGroupForJoin")

	query = query.Where("u.manager_id is null")

	if req.CustomerGroupId != nil {
		query = query.Where("customer_group_id = ?", req.CustomerGroupId)
	}

	if req.Username != nil {
		query = query.Where("username LIKE ?", fmt.Sprintf("%%%s%%", *req.Username))
	}

	if req.BidderId != nil {
		query = query.Where("bidder_id LIKE ?", fmt.Sprintf("%%%s%%", *req.BidderId))
	}

	if req.Name != nil {
		query = query.Where("CONCAT(first_name, middle_name, last_name) LIKE ?", fmt.Sprintf("%%%s%%", *req.Name))
	}

	if req.TaxId != nil {
		query = query.Where("identification_number LIKE ?", fmt.Sprintf("%%%s%%", *req.TaxId))
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (r *buyerRepositoryImpl) UpdateAllFields(buyer entity.Buyer) error {
	if err := r.DB.Save(buyer).Error; err != nil {
		return err
	}
	return nil
}

func (r *buyerRepositoryImpl) UpdateStatus(buyerId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Buyer{}).Where("id = ?", buyerId).Updates(fields).Error
	return err
}
func (r *buyerRepositoryImpl) UpdateStatusTx(tx *gorm.DB, buyerId int, fields map[string]interface{}) error {
	err := tx.Model(&entity.Buyer{}).Where("id = ?", buyerId).Updates(fields).Error
	return err
}

func (r *buyerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

func (r *buyerRepositoryImpl) FindBuyerDetailsWithFilter(req dto.BuyerSearchReqDto) (*entity.Buyer, error) {
	var results *entity.Buyer

	query := r.DB.Model(&entity.Buyer{}).Select(
		`
			buyer.*,
			CONCAT_WS(' ', buyer.first_name, buyer.middle_name, buyer.last_name) AS name,
			mpn.id AS prefix,
			mpn.description_th AS prefix_name_th,
			mpn.description_en AS prefix_name_en
	`).
		Joins("LEFT JOIN master_prefix_name mpn ON mpn.id = buyer.prefix_name_id").
		Preload("CustomerGroupForJoin")

	if req.Keyword != nil {
		kw := strings.TrimSpace(util.Val(req.Keyword))
		kw = strings.Join(strings.Fields(kw), " ")

		query = query.Where(
			`
				buyer.bidder_id = ? OR 
				buyer.identification_number = ? OR 
				buyer.customer_no = ? OR 
				CONCAT_WS(' ', buyer.first_name, buyer.middle_name, buyer.last_name) = ?
			`,
			kw,
			kw,
			kw,
			kw,
		)
	}

	if err := query.First(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}
