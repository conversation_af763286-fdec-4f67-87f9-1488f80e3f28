package dto

type BuyerSearchReqDto struct {
	Keyword *string `json:"keyword"`
}

type BuyerSearchRespDto struct {
	UserId               *int    `json:"userId"`
	BidderId             *string `json:"bidderId"`
	CustomerNo           *string `json:"customerNo"`
	IdentificationNumber *string `json:"identificationNumber"`
	CustomerGroup        *string `json:"customerGroup"`
	PrefixNameId         *int    `json:"prefixNameId"`
	PrefixNameTh         *string `json:"prefixNameTh"`
	PrefixNameEn         *string `json:"prefixNameEn"`
	FirstName            *string `json:"firstName"`
	MiddleName           *string `json:"middleName"`
	LastName             *string `json:"lastName"`
}
