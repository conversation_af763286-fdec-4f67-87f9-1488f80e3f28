package dto

import (
	constants "backend-common-lib/constants/registration_approval_status"
	"backend-common-lib/model"
	"time"
)

type BuyerRegistrationRequestPageReqDto struct {
	model.PagingRequest
	AccountTypeId  *int                         `json:"accountTypeId"`
	NationalityId  *int                         `json:"nationalityId"`
	ApprovalStatus constants.ApprovalStatusEnum `json:"approvalStatus"`
	Search         *string                      `json:"search"`
}

type BuyerRegistrationRequestDto struct {
	model.BaseDto
	RequestDate          *time.Time `json:"requestDate"`          // วันที่ส่งคำขอ
	IdIdCardFile         *string    `json:"idIdCardFile"`         // id ไฟล์เลขที่บัตรประชาชน
	IdCardFile           *string    `json:"idCardFile"`           // ไฟล์เลขที่บัตรประชาชน
	IdCardFileType       *string    `json:"idCardFileType"`       // ประเภทไฟล์เลข
	IdPermitDocFile      *string    `json:"idPermitDocFile"`      // id ไฟล์เอกสารอนุญาติ
	PermitDocFile        *string    `json:"permitDocFile"`        // ไฟล์เอกสารอนุญาติ
	PermitDocFileType    *string    `json:"permitDocFileType"`    // ประเภทไฟล์
	IdBankAccountFile    *string    `json:"idBankAccountFile"`    // id ไฟล์เอกสารบัญชีธนาคาร
	BankAccountFile      *string    `json:"bankAccountFile"`      // ไฟล์เอกสารบัญชีธนาคาร
	BankAccountFileType  *string    `json:"bankAccountFileType"`  // ประเภทไฟล์
	IdOtherAccountFile   *string    `json:"idOtherAccountFile"`   // id ไฟล์เอกสารอื่นๆ
	OtherAccountFile     *string    `json:"otherAccountFile"`     // ไฟล์เอกสารอื่นๆ
	OtherAccountFileType *string    `json:"otherAccountFileType"` // ประเภทไฟล์

	CustomerTypeId   *int       `json:"customerTypeId"`   // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	CustomerTypeCode *string    `json:"customerTypeCode"` // ประเภท
	CustomerTypeTh   *string    `json:"customerTypeTh"`   // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	CustomerTypeEn   *string    `json:"customerTypeEn"`   // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	NationalId       *string    `json:"nationalId"`       // เลขประจำตัวประชาชน / เลขนิติบุคคล
	TitleTh          *string    `json:"titleTh"`          // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	TitleEn          *string    `json:"titleEn"`          // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	FirstNameTh      *string    `json:"firstNameTh"`      // ชื่อ
	FirstNameEn      *string    `json:"firstNameEn"`      // ชื่อ
	MiddleNameTh     *string    `json:"middleNameTh"`     // ชื่อกลาง
	MiddleNameEn     *string    `json:"middleNameEn"`     // ชื่อกลาง
	LastNameTh       *string    `json:"lastNameTh"`       // นามสกุล
	LastNameEn       *string    `json:"lastNameEn"`       // นามสกุล
	DateOfBirth      *time.Time `json:"dateOfBirth"`      // วันเดือนปีเกิด
	DateOfExpiry     *time.Time `json:"dateOfExpiry"`     // วันหมดอายุบัตรประชาชน
	NationalityId    *int       `json:"nationalityId"`    // รหัสประเทศ
	NationalityTh    *string    `json:"nationalityTh"`    //
	NationalityEn    *string    `json:"nationalityEn"`    //
	NationalityCode  *string    `json:"nationalityCode"`  //

	GroupFileWorkPermitAndOthers []GroupFileWorkPermitAndOther `json:"groupFileWorkPermitAndOthers"`

	// Company & Contact info from query (detail)
	CompanyName               *string    `json:"companyName"`
	CompanyRegistrationDate   *time.Time `json:"companyRegistrationDate"`
	CompanyBusinessType       *string    `json:"companyBusinessType"`
	CompanyBranchCode         *string    `json:"companyBranchCode"`
	PhoneNumber               *string    `json:"phoneNumber"`
	Email                     *string    `json:"email"`
	RegisteredAddressTypeId   *int       `json:"registeredAddressTypeId"`
	RegisteredAddress         *string    `json:"registeredAddress"`   // ที่อยู่ตามทะเบียนบ้าน
	RegisteredAddressEn       *string    `json:"registeredAddressEn"` // ที่อยู่ตามทะเบียนบ้าน
	DocumentAddressTypeId     *int       `json:"documentAddressTypeId"`
	DocumentAddress           *string    `json:"documentAddress"`   // ที่อยู่ส่งเอกสาร (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	DocumentAddressEn         *string    `json:"documentAddressEn"` // ที่อยู่ส่งเอกสาร (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	RegistrationBookTypeId    *int       `json:"registrationBookTypeId"`
	RegistrationBookAddress   *string    `json:"registrationBookAddress"`   // ที่อยู่ส่งเล่มทะเบียน (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	RegistrationBookAddressEn *string    `json:"registrationBookAddressEn"` // ที่อยู่ส่งเล่มทะเบียน (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	ShippingAddressTypeId     *int       `json:"shippingAddressTypeId"`
	ShippingAddress           *string    `json:"shippingAddress"`   // ที่อยู่ส่งสินค้า (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	ShippingAddressEn         *string    `json:"shippingAddressEn"` // ที่อยู่ส่งสินค้า (ถ้าไม่มีให้ใช้ที่อยู่ตามทะเบียนบ้าน)
	ApprovalStatus            *string    `json:"approvalStatus"`    // รหัสสถานะ เช่น WAITING, APPROVED, REJECTED
	RejectReason              *string    `json:"rejectReason"`      // เหตุผลการปฏิเสธ (ถ้ามี)

	// Director info from query (detail)
	DirectorId                   *int    `json:"directorId"`
	DirectorPrefixTh             *string `json:"directorPrefixTh"`
	DirectorPrefixEn             *string `json:"directorPrefixEn"`
	DirectorFirstName            *string `json:"directorFirstName"`
	DirectorMiddleName           *string `json:"directorMiddleName"`
	DirectorLastName             *string `json:"directorLastName"`
	DirectorIdentificationNumber *string `json:"directorIdentificationNumber"`
	DirectorFileName             *string `json:"directorFileName"`
	DirectorFileType             *string `json:"directorFileType"`
	DirectorFileCategory         *string `json:"directorFileCategory"`

	// Bank information // Not have table
	BankAccountNumber *string `json:"bankAccountNumber"` // เลข
	BankName          *string `json:"bankName"`          // ชื่อธนาคาร
	AccountName       *string `json:"accountName"`       // ชื่อบัญชี

}

type BuyerRegistrationRequestPageRespDto[T any] struct {
	model.PagingModel[T]
}

type BuyerRegistrationRequestUpdateReqDto struct {
	model.BaseDtoActionBy
	ApprovalStatus constants.ApprovalStatusEnum `json:"approvalStatus" validate:"required,oneof=APPROVED REJECTED approved rejected"`
	Remark         *string                      `json:"remark" validate:"omitempty,min=1,max=500"`
}

type GroupFileWorkPermitAndOther struct {
	// ระบุประเภทไฟล์ของกลุ่มนี้ เช่น WORK_PERMIT หรือ OTHER
	FileCategory string `json:"fileCategory"` // WORK_PERMIT | OTHER

	// ข้อมูลไฟล์พื้นฐาน
	FileId   *string `json:"fileId"`   // id ของไฟล์
	FileName *string `json:"fileName"` // ชื่อไฟล์
	FileType *string `json:"fileType"` // ประเภทไฟล์ (เช่น pdf, jpg)

}

// FlowChart Repo Dto
type RequestFlowChartRepoDto struct {
	NationalityId    *int    `column:"nationality_id" json:"nationalityId"`
	Total            *int    `column:"total" json:"total"`
	TotalPerType     *int    `column:"total_per_type" json:"totalPerType"`
	CustomerTypeId   *int    `column:"customer_type_id" json:"customerTypeId"`
	CustomerTypeCode *string `column:"customer_type_code" json:"customerTypeCode"`
	CustomerTypeTh   *string `column:"customer_type_th" json:"customerTypeTh"`
	CustomerTypeEn   *string `column:"customer_type_en" json:"customerTypeEn"`
	TotalPerStatus   *int    `column:"total_per_status" json:"totalPerStatus"`
	ApprovalStatus   *string `column:"approval_status" json:"approvalStatus"`
}

// Flowchart Resp Dto
// // BuyerRegistrationRequest(New)
type FlowchartCustomerTypeCalRespDto struct {
	CustomerTypeTh      *string  `json:"customerTypeTh"`
	CustomerTypeEn      *string  `json:"customerTypeEn"`
	CustomerTypePercent *float64 `json:"customerTypePercent"`
}

type FlowchartCustomerTypeCalAllRespDto struct {
	Total       *int                             `json:"total"`
	Individual  *FlowchartCustomerTypeCalRespDto `json:"individual"`
	Foreigner   *FlowchartCustomerTypeCalRespDto `json:"foreigner"`
	LegalEntity *FlowchartCustomerTypeCalRespDto `json:"legalEntity"`
}

// // BuyerRegistrationRequest(Waiting), BuyerEditProfileRequest(Editing)
type FlowchartApprovalStatusCalRespDto struct {
	ApprovalStatusTh      *string  `json:"approvalStatusTh"`
	ApprovalStatusEn      *string  `json:"approvalStatusEn"`
	ApprovalStatusPercent *float64 `json:"approvalStatusPercent"`
}

type FlowchartApprovalStatusCalAllRespDto struct {
	Total   *int                               `json:"total"`
	Waiting *FlowchartApprovalStatusCalRespDto `json:"waiting"`
	Approve *FlowchartApprovalStatusCalRespDto `json:"approve"`
	Reject  *FlowchartApprovalStatusCalRespDto `json:"reject"`
}

// // Budget Increasing Credit Limit
type BudgetIncreasingCreditLimitRequestAllRespDto struct {
	Total   *int                               `json:"total"`
	Auto    *FlowchartApprovalStatusCalRespDto `json:"auto"`
	Waiting *FlowchartApprovalStatusCalRespDto `json:"waiting"`
	Approve *FlowchartApprovalStatusCalRespDto `json:"approve"`
	Reject  *FlowchartApprovalStatusCalRespDto `json:"reject"`
}

// Flowchart Page Resp Dto
type BuyerRegistrationRequestFlowChartPageRespDto struct {
	NewBuyerRegistrationRequest        *FlowchartCustomerTypeCalAllRespDto           `json:"newBuyerRegistrationRequest"`
	WaitingBuyerRegistrationRequest    *FlowchartApprovalStatusCalAllRespDto         `json:"waitingBuyerRegistrationRequest"`
	EditingBuyerEditProfileRequest     *FlowchartApprovalStatusCalAllRespDto         `json:"editingBuyerEditProfileRequest"`
	BudgetIncreasingCreditLimitRequest *BudgetIncreasingCreditLimitRequestAllRespDto `json:"budgetIncreasingCreditLimitRequest"`
}
