package dropdown

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	dropdownService "content-service/internal/service/dropdown"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service dropdownService.DropdownService
}

func (h *Handler) GetAssetTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAssetTypeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAssetGroupDropdown(c.Query("isActive"), c.Query("assetTypeCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetGroupDropdownByMultipleAssetType(c *fiber.Ctx) error {
	var req dto.AssetGroupMultiSelectDropdownReqDto

	if err := c.BodyParser(&req); err != nil {
		return err
	}

	res, err := h.Service.GetAssetGroupDropdownByMultipleAssetType(c.Query("isActive"), req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRegionDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRegionDropdown(c.Query("isActive"), c.Query("countryCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCountryDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCountryDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetEventDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetEventDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVendorDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVendorDropdownWithFilter(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVendorGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVendorGroupDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSaleChannelDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSaleChannelDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetBranchDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetBranchDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetLocationDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAssetLocationDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetLotSettingDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetLotSettingDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetFloorStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetFloorStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigFeatureLotDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigFeatureLotDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSaleChannelConfigDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSaleChannelConfigDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetPrefixNameDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetPrefixNameDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVatBusinessDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVatBusinessDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDayBeforeDueDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDayBeforeDueDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetFloorDropdown(c *fiber.Ctx) error {

	res, err := h.Service.GetFloorDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetFloorByBranchAndAuctionDateDropdown(c *fiber.Ctx) error {

	res, err := h.Service.GetFloorByBranchAndAuctionDateDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetVatCodeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetVatCodeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDistrictDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDistrictDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCityDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCityDropdown(c.Query("isActive"), c.Query("regionCode"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetNationalityDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetNationalityDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSubDistrictDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSubDistrictDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCustomerTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCustomerTypeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCustomerGroupDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCustomerGroupDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetDepartmentDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetDepartmentDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionNameDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionNameDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetBuyerDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetBuyerDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetProxyBidStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetProxyBidStatusDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetActionStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetActionStatusDropdown(c)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetProxyBidCancelReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetProxyBidCancelReasonDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRoleDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRoleDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAuctionCollateralDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigAuctionCollateralDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigDisplayLocationDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigDisplayLocationDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigCampaignEventTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigCampaignEventTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAuctionTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigAuctionTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigAdditionalServiceTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigServiceTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetHelpRequestStatusDropdown(c *fiber.Ctx) error {

	res, err := h.Service.GetHelpRequestStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetHelpRequestReasonDropdown(c *fiber.Ctx) error {
	_, _, roleIds := util.GetUserDetailFromHeader(c)
	res, err := h.Service.GetHelpRequestReasonDropdown(c.Query("isActive"), roleIds, c.QueryBool("isFilterByRole"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetReprintSlipReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetReprintSlipReasonDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionBidVoidReasonDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionBidVoidReasonDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAnswerTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAnswerTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSurveyTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSurveyTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetSurveyCriteriaDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetSurveyCriteriaDropdown(c.Query("surveyType"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterCityByPostCode(c *fiber.Ctx) error {
	postCode := c.Query("postCode")

	if postCode == "" {
		return errs.NewError(http.StatusBadRequest, nil)
	}

	res, err := h.Service.GetProvinceDropdownByPostCode(postCode)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterDistrictByPostCodeAndCityID(c *fiber.Ctx) error {
	postCode := c.Query("postCode")
	cityID := c.Query("cityId")

	var parsedCityID int

	if cityID == "" {
		return errs.NewError(http.StatusBadRequest, nil)
	} else {
		parsed, convErr := strconv.Atoi(cityID)
		if convErr != nil {
			return errs.NewError(http.StatusBadRequest, nil)
		}

		parsedCityID = parsed
	}

	res, err := h.Service.GetDistrictDropdownByPostCodeAndCityID(&postCode, parsedCityID)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterSubDistrictByPostCodeAndDistrictID(c *fiber.Ctx) error {
	postCode := c.Query("postCode")
	districtID := c.Query("districtId")

	var parsedDistrictID int

	if districtID == "" {
		return errs.NewError(http.StatusBadRequest, nil)
	} else {
		parsed, convErr := strconv.Atoi(districtID)
		if convErr != nil {
			return errs.NewError(http.StatusBadRequest, nil)
		}

		parsedDistrictID = parsed
	}

	res, err := h.Service.GetSubDistrictDropdownByPostCodeAndDistrictID(&postCode, parsedDistrictID)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRegistrationRequestDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRegistrationRequestDropdown()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCreditLimitRequestDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCreditLimitRequestDropdown()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetCustomerGradeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetCustomerGradeDropdown()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetEditProfileDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetEditProfileDropdown()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetBuyerPurchaseStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetBuyerPurchaseDropdown()
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAuctionDepositStatusDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetAuctionDepositStatusDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetPaymentTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetPaymentTypeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRegisterTypeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRegisterTypeDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetRegisterTypeCarDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetRegisterTypeCarDropdown(c.Query("isActive"))
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterPaymentMethodDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	res, err := h.Service.GetMasterPaymentMethodDropdown(isActive)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterBankDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	res, err := h.Service.GetMasterBankDropdown(isActive)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

// Brand
func (h *Handler) GetMasterBrandDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	res, err := h.Service.GetMasterBrandDropdown(isActive)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

// Model
func (h *Handler) GetMasterModelDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	brandCode := c.Query("brandCode")
	res, err := h.Service.GetMasterModelDropdown(isActive, brandCode)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigCarTypeConDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigCarTypeConDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetConfigStructureGradeDropdown(c *fiber.Ctx) error {
	res, err := h.Service.GetConfigStructureGradeDropdown()
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAssetTypeDropdownInAuction(c *fiber.Ctx) error {

	res, err := h.Service.GetAssetTypeDropdownInAuction(c.Query("auctionId"))
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterCarPaintDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	res, err := h.Service.GetMasterCarPaintDropdown(isActive)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetMasterVoidReasonDropdown(c *fiber.Ctx) error {
	isActive := c.Query("isActive")
	res, err := h.Service.GetMasterVoidReasonDropdown(isActive)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
