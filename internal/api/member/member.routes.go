package member

import (
	buyerRepository "content-service/internal/repository/buyer"
	buyerRegistrationRequestRepository "content-service/internal/repository/buyer_registration_request"
	service "content-service/internal/service/member"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	buyerRepo := buyerRepository.NewBuyerRepository(db)
	buyerRegistrationRequestRepo := buyerRegistrationRequestRepository.NewBuyerRegistrationRequestRepository(db)
	service := service.NewMemberService(buyerRepo, buyerRegistrationRequestRepo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	r.Post("/search", h.SearchMemberWithFilter)
	r.Put("/status/:id", h.UpdateMemberStatus)

	r.Get("/summary-chart", h.GetBuyerRegistrationRequestFlowChart)
}
