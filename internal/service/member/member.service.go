package service

import (
	"content-service/internal/model/dto"
	buyerRepository "content-service/internal/repository/buyer"
	buyerRegistrationRequestRepository "content-service/internal/repository/buyer_registration_request"
)

type memberService struct {
	BuyerRepo                    buyerRepository.BuyerRepository
	BuyerRegistrationRequestRepo buyerRegistrationRequestRepository.BuyerRegistrationRequestRepository
}

type MemberService interface {
	SearchMemberWithFilter(req dto.MemberSearchReqDto) (dto.MemberPageRespDto[dto.MemberSearchRespDto], error)
	UpdateMemberStatus(req dto.UpdateMemberStatusReqDto) error

	GetBuyerRegistrationRequestFlowChart() (dto.BuyerRegistrationRequestFlowChartPageRespDto, error)
}

func NewMemberService(
	buyerRepo buyerRepository.BuyerRepository,
	buyerRegistrationRequestRepo buyerRegistrationRequestRepository.BuyerRegistrationRequestRepository,
) MemberService {
	return &memberService{
		BuyerRepo:                    buyerRepo,
		BuyerRegistrationRequestRepo: buyerRegistrationRequestRepo,
	}
}
