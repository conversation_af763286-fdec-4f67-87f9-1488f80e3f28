package dropdown

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	constants_content "content-service/constant"
	"fmt"
	"net/http"
	"strings"
	"time"

	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
)

func (s *dropdownService) GetAssetTypeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetTypeRepo.GetDB().Model(&entity.MasterAssetType{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var assetTypes []entity.MasterAssetType
	if err := query.Order("asset_type_code asc").Find(&assetTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, assetType := range assetTypes {
		if assetType.AssetTypeCode == nil || assetType.DescriptionTh == nil || assetType.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetType.Id,
			Value:   *assetType.AssetTypeCode,
			LabelTh: *assetType.DescriptionTh,
			LabelEn: *assetType.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetGroupDropdown(isActive string, assetTypeCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetGroupRepo.GetDB().Model(&entity.MasterAssetGroup{})
	if assetTypeCode != "" {
		query = query.Where("asset_type_code = ?", assetTypeCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var assetGroups []entity.MasterAssetGroup
	if err := query.Order("asset_group_code asc").Find(&assetGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, assetGroup := range assetGroups {
		if assetGroup.AssetGroupCode == nil || assetGroup.DescriptionTh == nil || assetGroup.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetGroup.Id,
			Value:   *assetGroup.AssetGroupCode,
			LabelTh: *assetGroup.DescriptionTh,
			LabelEn: *assetGroup.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetGroupDropdownByMultipleAssetType(isActive string, req dto.AssetGroupMultiSelectDropdownReqDto) ([]dto.DropdownAssetGroupDto, error) {
	var dropdowns []dto.DropdownAssetGroupDto

	query := s.AssetGroupRepo.GetDB().Model(&entity.MasterAssetGroup{}).
		Select("master_asset_group.*, master_asset_type.id as asset_type_id").
		Joins("JOIN master_asset_type ON master_asset_type.asset_type_code = master_asset_group.asset_type_code")
	if len(req.AssetTypeIds) != 0 {
		query = query.Where("master_asset_type.id IN ?", req.AssetTypeIds)
	}
	if isActive != "" {
		query = query.Where("master_asset_group.is_active = ?", isActive)
	}

	var assetGroups []entity.MasterAssetGroup
	if err := query.Order("asset_group_code asc").Find(&assetGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, assetGroup := range assetGroups {
		if assetGroup.AssetGroupCode == nil || assetGroup.DescriptionTh == nil || assetGroup.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, dto.DropdownAssetGroupDto{
			Id:          assetGroup.Id,
			Value:       util.Val(assetGroup.AssetGroupCode),
			LabelTh:     util.Val(assetGroup.DescriptionTh),
			LabelEn:     util.Val(assetGroup.DescriptionEn),
			AssetTypeId: util.Val(assetGroup.AssetTypeId),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetEventDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.EventRepo.GetDB().Model(&entity.MasterEvent{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var events []entity.MasterEvent
	if err := query.Order("event_code asc").Find(&events).Error; err != nil {
		return dropdowns, err
	}

	for _, event := range events {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      event.Id,
			Value:   *event.EventCode,
			LabelTh: *event.DescriptionTh,
			LabelEn: *event.DescriptionEn,
		})
	}

	return dropdowns, nil
}

// TODO - To delete
func (s *dropdownService) GetVendorDropdown(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	isActive := c.Query("isActive", "")
	page := c.Query("page", "")
	size := c.Query("size", "")
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}

	query := s.VendorRepo.GetDB().Model(&entity.MasterVendor{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var vendors []entity.MasterVendor
	if err := query.Order("vendor_no asc").Find(&vendors).Error; err != nil {
		return res, err
	}

	for _, vendor := range vendors {
		if vendor.VendorNo == nil || vendor.VendorName == nil {
			continue
		}

		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      vendor.Id,
			Value:   *vendor.VendorNo,
			LabelTh: *vendor.VendorName,
			LabelEn: *vendor.VendorName,
		})
	}

	return res, nil
}

func (s *dropdownService) GetCountryDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CountryRepo.GetDB().Model(&entity.MasterCountry{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var countries []entity.MasterCountry
	if err := query.Order("country_code asc").Find(&countries).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range countries {
		if m.CountryCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CountryCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVendorGroupDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VendorGroupRepo.GetDB().Model(&entity.MasterVendorGroup{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vendorGroups []entity.MasterVendorGroup
	if err := query.Order("vendor_group_code asc").Find(&vendorGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, vendorGroup := range vendorGroups {
		if vendorGroup.VendorGroupCode == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      vendorGroup.Id,
			Value:   util.Val(vendorGroup.VendorGroupCode),
			LabelTh: util.Val(vendorGroup.DescriptionTh),
			LabelEn: util.Val(vendorGroup.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSaleChannelDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.SaleChannelRepo.GetDB().Model(&entity.MasterSaleChannel{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var saleChannels []entity.MasterSaleChannel
	if err := query.Order("sale_channel_code asc").Find(&saleChannels).Error; err != nil {
		return dropdowns, err
	}

	for _, saleChannel := range saleChannels {
		if saleChannel.SaleChannelCode == nil || saleChannel.DescriptionTh == nil || saleChannel.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      saleChannel.Id,
			Value:   *saleChannel.SaleChannelCode,
			LabelTh: *saleChannel.DescriptionTh,
			LabelEn: *saleChannel.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetBranchDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	isActive := c.Query("isActive")
	isSaleLocation := c.Query("isSaleLocation")
	branchId := c.Query("branchId")

	query := s.BranchRepo.GetDB().Model(&entity.MasterBranch{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	if branchId != "" {
		branchIdSlice := strings.Split(branchId, ",")
		query = query.Where("id IN (?)", branchIdSlice)
	}
	if isSaleLocation != "" {
		query = query.Where("is_sale_location = ?", isSaleLocation)
	}

	var branches []entity.MasterBranch
	if err := query.Order("description_th asc").Find(&branches).Error; err != nil {
		return dropdowns, err
	}

	for _, branch := range branches {
		if branch.BranchCode == nil || branch.DescriptionTh == nil || branch.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      branch.Id,
			Value:   *branch.BranchCode,
			LabelTh: *branch.DescriptionTh,
			LabelEn: *branch.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetLocationDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	isActive := c.Query("isActive")
	isSaleLocation := c.Query("isSaleLocation")
	branchId := c.Query("branchId")
	branchCode := c.Query("branchCode")

	query := s.AssetLocationRepo.GetDB().Model(&entity.MasterAssetLocation{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if branchId != "" {
		branchIdInt, err := strconv.Atoi(branchId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		branchResult, err := s.BranchRepo.FindById(branchIdInt)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		if branchResult != nil {
			query = query.Where("branch_code = ?", branchResult.BranchCode)
		}
	}

	if branchCode != "" {
		query = query.Where("branch_code = ?", branchCode)
	}

	if isSaleLocation != "" {
		query = query.Where("is_sale_location = ?", isSaleLocation)
	}

	var assetLocations []entity.MasterAssetLocation
	if err := query.Order("description_th asc").Find(&assetLocations).Error; err != nil {
		return dropdowns, err
	}

	for _, assetLocation := range assetLocations {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetLocation.Id,
			Value:   util.Val(assetLocation.LocationCode),
			LabelTh: util.Val(assetLocation.DescriptionTh),
			LabelEn: util.Val(assetLocation.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetLotSettingDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.LotSettingRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name IN (?, ?, ?)", constant.ConfigParamConst.FLOOR_STATUS, constant.ConfigParamConst.CONFIG_FEATURE_LOT, constant.ConfigParamConst.SALE_CHANNEL)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetFloorStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.FloorStatusRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.FLOOR_STATUS)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigFeatureLotDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.CONFIG_FEATURE_LOT)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSaleChannelConfigDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.SaleChannelRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.SALE_CHANNEL)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.AuctionStatusRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetPrefixNameDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PrefixNameRepo.GetDB().Model(&entity.MasterPrefixName{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var prefixNames []entity.MasterPrefixName
	if err := query.Order("prefix_name_code asc").Find(&prefixNames).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range prefixNames {
		if m.PrefixNameCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.PrefixNameCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVatBusinessDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VatBusinessRepo.GetDB().Model(&entity.MasterVatBusiness{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vatBusinesses []entity.MasterVatBusiness
	if err := query.Order("vat_business_code asc").Find(&vatBusinesses).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range vatBusinesses {
		if m.VatBusinessCode == nil || m.Description == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.VatBusinessCode,
			LabelTh: *m.Description,
			LabelEn: *m.Description,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDayBeforeDueDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.DayBeforeDueRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DAYS_BEFORE_DUE)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRegionDropdown(isActive string, countryCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.RegionRepo.GetDB().Model(&entity.MasterRegion{})
	if countryCode != "" {
		query = query.Where("country_code = ?", countryCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var regions []entity.MasterRegion
	if err := query.Order("region_code asc").Find(&regions).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range regions {
		if m.RegionCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.RegionCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetFloorDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	isActive := c.Query("isActive")
	branchId := c.Query("branchId")
	branchCode := c.Query("branchCode")
	assetLocationId := c.Query("assetLocationId")
	assetLocationCode := c.Query("assetLocationCode")
	auctionDate := c.Query("auctionDate")

	query := s.FloorRepo.GetDB().Model(&entity.MasterAssetLocationFloor{})

	if branchId != "" {
		branchIdInt, err := strconv.Atoi(branchId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		branchResult, err := s.BranchRepo.FindById(branchIdInt)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		if branchResult != nil {
			query = query.Where("branch_code = ?", branchResult.BranchCode)
		}
	}

	if branchCode != "" {
		query = query.Where("branch_code = ?", branchCode)
	}

	if assetLocationId != "" {
		assetLocationIdInt, err := strconv.Atoi(assetLocationId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		subQuery := s.FloorRepo.GetDB().
			Table("master_asset_location").
			Select("id as mal_id, location_code")
		query = query.
			Joins("JOIN (?) al ON master_asset_location_floor.location_floor_code = al.location_code", subQuery).
			Where("al.mal_id = ?", assetLocationIdInt)
	}

	if assetLocationCode != "" {
		query = query.Where("location_floor_code = ?", assetLocationCode)
	}

	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if auctionDate != "" {
		lotResult := []entity.Lot{}
		lotQuery := s.LotRepo.GetDB().Model(&entity.Lot{}).
			Select("DISTINCT floor_id").
			Where("lot.auction_date = ?", auctionDate)

		if err := lotQuery.Find(&lotResult).Error; err != nil {
			return dropdowns, err
		}
		floorIds := []int{}
		for _, lot := range lotResult {
			floorIds = append(floorIds, lot.FloorId)
		}
		if len(floorIds) > 0 {
			query = query.Where("id in (?)", floorIds)
		}
	}

	var floors []entity.MasterAssetLocationFloor
	if err := query.Order("floor asc").Find(&floors).Error; err != nil {
		return dropdowns, err
	}

	mapCheckExist := make(map[string]bool, 10)
	for _, m := range floors {
		if _, ok := mapCheckExist[util.Val(m.Floor)]; !ok && m.Floor != nil {
			dropdowns = append(dropdowns, model.DropdownDto{
				Id:      m.Id,
				Value:   util.Val(m.Floor),
				LabelTh: "ลาน " + util.Val(m.Floor),
				LabelEn: "Yard " + util.Val(m.Floor),
			})
			mapCheckExist[util.Val(m.Floor)] = true
		}

	}

	return dropdowns, nil
}

func (s *dropdownService) GetFloorByBranchAndAuctionDateDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	isActive := c.Query("isActive")
	branchId := c.Query("branchId")
	auctionDate := c.Query("auctionDate")
	branchCode := c.Query("branchCode")
	assetLocationId := c.Query("assetLocationId")
	assetLocationCode := c.Query("assetLocationCode")

	lotResult := []entity.Lot{}
	lotQuery := s.LotRepo.GetDB().Model(&entity.Lot{})

	if auctionDate != "" {
		lotQuery = s.LotRepo.GetDB().Model(&entity.Lot{}).
			Select("DISTINCT floor_id").
			Where("lot.auction_date = ?", auctionDate)

		if err := lotQuery.Find(&lotResult).Error; err != nil {
			return dropdowns, err
		}
	}

	floorIds := []int{}
	for _, lot := range lotResult {
		floorIds = append(floorIds, lot.FloorId)
	}

	query := s.FloorRepo.GetDB().Model(&entity.MasterAssetLocationFloor{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if branchId != "" {
		branchIdInt, err := strconv.Atoi(branchId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		branchResult, err := s.BranchRepo.FindById(branchIdInt)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		if branchResult != nil {
			query = query.Where("branch_code = ?", branchResult.BranchCode)
		}
	}

	if branchCode != "" {
		query = query.Where("branch_code = ?", branchCode)
	}

	if assetLocationId != "" {
		assetLocationIdInt, err := strconv.Atoi(assetLocationId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		subQuery := s.FloorRepo.GetDB().
			Table("master_asset_location").
			Select("id as mal_id, location_code")
		query = query.
			Joins("JOIN (?) al ON master_asset_location_floor.location_floor_code = al.location_code", subQuery).
			Where("al.mal_id = ?", assetLocationIdInt)
	}

	if assetLocationCode != "" {
		query = query.Where("location_floor_code = ?", assetLocationCode)
	}

	query = query.Order("floor asc")
	if len(floorIds) > 0 {
		query = query.Where("id in (?)", floorIds)
	}

	var floors []entity.MasterAssetLocationFloor
	if err := query.Find(&floors).Error; err != nil {
		return dropdowns, err
	}
	mapCheckExist := make(map[string]bool, 10)
	for _, m := range floors {
		if _, ok := mapCheckExist[util.Val(m.Floor)]; !ok && m.Floor != nil {
			dropdowns = append(dropdowns, model.DropdownDto{
				Id:      m.Id,
				Value:   util.Val(m.Floor),
				LabelTh: "ลาน " + util.Val(m.Floor),
				LabelEn: "Yard " + util.Val(m.Floor),
			})
			mapCheckExist[util.Val(m.Floor)] = true
		}
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVatCodeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.VatCodeRepo.GetDB().Model(&entity.MasterVatCode{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var vatCodes []entity.MasterVatCode
	if err := query.Order("vat_code asc").Find(&vatCodes).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range vatCodes {
		if m.VatCode == nil || m.Description == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.VatCode,
			LabelTh: *m.Description,
			LabelEn: *m.Description,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDistrictDropdown(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	cityCode := c.Query("cityCode")
	isActive := c.Query("isActive")
	page := c.Query("page", "")
	size := c.Query("size", "")
	selectedRecord := c.Query("selectedRecord", "")
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}

	query := s.DistrictRepo.GetDB().Model(&entity.MasterDistrict{})
	if selectedRecord != "" {
		query = query.Select(`*, 
			CASE 
				WHEN id IN (` + selectedRecord + `) THEN 0 
				ELSE NULL 
			END AS custom_page`).Order("custom_page asc")
	}
	if cityCode != "" {
		query = query.Where("city_code = ?", cityCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var districts []entity.MasterDistrict
	if err := query.Order("district_code asc").Find(&districts).Error; err != nil {
		return res, err
	}

	for _, m := range districts {
		if m.DistrictCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.DistrictCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return res, nil
}

func (s *dropdownService) GetCityDropdown(isActive string, regionCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CityRepo.GetDB().Model(&entity.MasterCity{})
	if regionCode != "" {
		query = query.Where("region_code = ?", regionCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var cities []entity.MasterCity
	if err := query.Order("city_code asc").Find(&cities).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range cities {
		if m.CityCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CityCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetNationalityDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CountryRepo.GetDB().Model(&entity.MasterCountry{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var countries []entity.MasterCountry
	if err := query.Order("country_code asc").Find(&countries).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range countries {
		// Require country code and nationality labels
		if m.CountryCode == nil || m.NationalityTh == nil || m.NationalityEn == nil {
			continue
		}
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CountryCode,
			LabelTh: *m.NationalityTh,
			LabelEn: *m.NationalityEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSubDistrictDropdown(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	districtCode := c.Query("districtCode")
	isActive := c.Query("isActive")
	page := c.Query("page", "")
	size := c.Query("size", "")
	selectedRecord := c.Query("selectedRecord", "")
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}

	query := s.SubDistrictRepo.GetDB().Model(&entity.MasterSubDistrict{})
	if selectedRecord != "" {
		query = query.Select(`*, 
			CASE 
				WHEN id IN (` + selectedRecord + `) THEN 0 
				ELSE NULL 
			END AS custom_page`).Order("custom_page asc")
	}
	if districtCode != "" {
		query = query.Where("district_code = ?", districtCode)
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var subDistricts []entity.MasterSubDistrict
	if err := query.Order("sub_district_code asc").Find(&subDistricts).Error; err != nil {
		return res, err
	}

	for _, m := range subDistricts {
		if m.SubDistrictCode == nil || m.DescriptionTh == nil || m.DescriptionEn == nil {
			continue
		}

		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.SubDistrictCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionEn,
		})
	}

	return res, nil
}

func (s *dropdownService) GetCustomerTypeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CustomerTypeRepo.GetDB().Model(&entity.MasterCustomerType{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var customerTypes []entity.MasterCustomerType
	if err := query.Order("customer_type_code asc").Find(&customerTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range customerTypes {
		if m.CustomerTypeCode == nil || m.DescriptionTh == nil {
			continue
		}

		labelEn := ""
		if m.DescriptionEn != nil {
			labelEn = *m.DescriptionEn
		} else {
			labelEn = *m.DescriptionTh
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CustomerTypeCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: labelEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetCustomerGroupDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.CustomerGroupRepo.GetDB().Model(&entity.MasterCustomerGroup{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var customerGroups []entity.MasterCustomerGroup
	if err := query.Order("customer_group_code asc").Find(&customerGroups).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range customerGroups {
		if m.CustomerGroupCode == nil || m.DescriptionTh == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.CustomerGroupCode,
			LabelTh: *m.DescriptionTh,
			LabelEn: *m.DescriptionTh,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDepartmentDropdown(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	isActive := c.Query("isActive", "")
	page := c.Query("page", "")
	size := c.Query("size", "")
	selectedRecord := c.Query("selectedRecord", "")
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}

	query := s.DepartmentRepo.GetDB().Model(&entity.MasterDepartment{})

	if selectedRecord != "" {
		query = query.Select(`*, 
			CASE 
				WHEN id IN (` + selectedRecord + `) THEN 0 
				ELSE NULL 
			END AS custom_page`).Order("custom_page asc")
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var departments []entity.MasterDepartment
	if err := query.Order("department_code asc").Find(&departments).Error; err != nil {
		return res, err
	}

	for _, m := range departments {
		if m.DepartmentCode == nil {
			continue
		}

		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.DepartmentCode,
			LabelTh: *m.DepartmentNameTh,
			LabelEn: *m.DepartmentNameEn,
		})
	}

	return res, nil
}

func (s *dropdownService) GetAuctionNameDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AuctionNameRepo.GetDB().Model(&entity.Auction{})

	query = query.Where("is_active = ?", true)

	auctionTypeId := c.Query("auctionTypeId", "")
	auctionDateStr := c.Query("auctionDate", "")

	if auctionTypeId != "" {
		auctionTypeIdInt, err := strconv.Atoi(auctionTypeId)
		if err != nil {
			return dropdowns, fiber.NewError(fiber.StatusBadRequest, err.Error())
		}
		query = query.Where("auction_type_id = ?", auctionTypeIdInt)
	}

	if auctionDateStr != "" {
		layoutDMY := constant.DateFormatDMY
		layoutYMD := constant.DateFormatYMD

		auctionDate, _ := time.Parse(layoutDMY, auctionDateStr)
		query = query.Where("DATE(start_date) <= DATE(?)", auctionDate.Format(layoutYMD))

	}

	var auctionNames []entity.Auction
	if err := query.Order("auction_name asc").Find(&auctionNames).Error; err != nil {
		return dropdowns, err
	}

	if len(auctionNames) == 0 {
		return dropdowns, nil
	}

	for _, m := range auctionNames {
		labelTh := m.AuctionName
		labelEn := ""

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.AuctionName),
			LabelTh: util.Val(labelTh),
			LabelEn: labelEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetBuyerDropdown(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}

	isDepositPaid := c.Query("isDepositPaid", "")
	customerGroupId := c.Query("customerGroupId", "")
	filter := c.Query("buyerInfo", "")
	page := c.Query("page", "")
	size := c.Query("size", "")
	selectedRecord := c.Query("selectedRecord", "")

	query := s.BuyerRepo.GetDB().Model(&entity.Buyer{})
	if selectedRecord != "" {
		query = query.Select(`*, 
			CASE 
				WHEN id IN (` + selectedRecord + `) THEN 0 
				ELSE NULL 
			END AS custom_page`).Order("custom_page asc")
	}
	if isDepositPaid != "" {
		query = query.Where("is_deposit_paid = ?", isDepositPaid)
	}

	if customerGroupId != "" {
		customerGroupIdInt, err := strconv.Atoi(customerGroupId)
		if err != nil {
			log.Error("Invalid customer group ID:", err)
			return res, err
		}
		query = query.Where("customer_group_id = ?", customerGroupIdInt)
	}
	if filter != "" {
		likeFilter := fmt.Sprintf("%%%s%%", filter)
		filterConditions := `
		bidder_id ILIKE ? 
		OR first_name ILIKE ? 
		OR last_name ILIKE ? 
		OR CONCAT_WS(' ', first_name, last_name) ILIKE ?
		OR CONCAT_WS(' : ', bidder_id, first_name) ILIKE ?
		OR CONCAT_WS(' : ', bidder_id, CONCAT_WS(' ', first_name, last_name)) ILIKE ?
	`

		if selectedRecord != "" {
			query = query.Where(filterConditions+` OR id IN (`+selectedRecord+`)`, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter)
		} else {
			query = query.Where(filterConditions, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter)
		}
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var buyers []entity.Buyer
	if err := query.Order("bidder_id asc").Find(&buyers).Error; err != nil {
		return res, err
	}

	for _, m := range buyers {
		if m.BidderId == nil {
			continue
		}
		var fullName string
		if m.FirstName != nil {
			fullName += " : " + *m.FirstName + " "
		}
		if m.LastName != nil {
			fullName += *m.LastName
		}

		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.BidderId,
			LabelTh: *m.BidderId + fullName,
			LabelEn: *m.BidderId + fullName,
		})
	}

	return res, nil
}

func (s *dropdownService) GetProxyBidStatusDropdown(c *fiber.Ctx) ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigParameterRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_PROXY_BID_STATUS)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetActionStatusDropdown(c *fiber.Ctx) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	for _, actionStatus := range constants_content.DropdownActionStatus {
		dropdowns = append(dropdowns, model.DropdownDto{
			Value:   actionStatus.Value,
			LabelTh: actionStatus.LabelTh,
			LabelEn: actionStatus.LabelEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetProxyBidCancelReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.ProxyBidCancelReasonRepo.GetDB().Model(&entity.ProxyBidCancelReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var proxyBidCancelReason []entity.ProxyBidCancelReason
	if err := query.Order("reason asc").Find(&proxyBidCancelReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range proxyBidCancelReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRoleDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetTypeRepo.GetDB().Model(&entity.Role{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	query = query.Where("deleted_date IS null")

	var roles []entity.Role
	if err := query.Order("name asc").Find(&roles).Error; err != nil {
		return dropdowns, err
	}

	for _, role := range roles {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      role.Id,
			Value:   role.Name,
			LabelTh: role.Name,
			LabelEn: role.Name,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigAuctionCollateralDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_COLLATERAL)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigDisplayLocationDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DISPLAY_LOCATION)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigCampaignEventTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.CAMPAIGN_EVENT_TYPE)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigAuctionTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_TYPE)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetConfigServiceTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.SERVICE_TYPE)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetHelpRequestStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_HELP_REQUEST_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetHelpRequestReasonDropdown(isActive string, roleIds []*int, isFilterByRole bool) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.HelpRequestReasonRepo.GetDB().Model(&entity.HelpRequestReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	if len(roleIds) > 0 && isFilterByRole {
		query = query.Joins("JOIN help_request_reason_role hrr ON hrr.help_request_reason_id = help_request_reason.id").
			Where("hrr.deleted_date IS NULL AND hrr.role_id IN (?)", roleIds)
	}

	var helpRequestReason []entity.HelpRequestReason
	if err := query.Select("DISTINCT help_request_reason.id, help_request_reason.reason").
		Order("help_request_reason.reason asc").
		Find(&helpRequestReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range helpRequestReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetReprintSlipReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.ReprintSlipReasonRepo.GetDB().Model(&entity.ReprintSlipReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var reprintSlipReason []entity.ReprintSlipReason
	if err := query.Order("reason asc").Find(&reprintSlipReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range reprintSlipReason {
		if m.Reason == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   *m.Reason,
			LabelTh: *m.Reason,
			LabelEn: *m.Reason,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionBidVoidReasonDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.DROPDOWN_AUCTION_BID_VOID_REASON_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAnswerTypeDropdown() ([]model.DropdownDto, error) {
	return constants_content.DropdownAnswerType, nil
}

func (s *dropdownService) GetRegistrationRequestDropdown() ([]model.DropdownDto, error) {
	return constants_content.DropdownRegistrationRequest, nil
}

func (s *dropdownService) GetCreditLimitRequestDropdown() ([]model.DropdownDto, error) {
	return constants_content.DropdownCreditLimitRequest, nil
}

func (s *dropdownService) GetCustomerGradeDropdown() ([]model.DropdownDto, error) {
	return constants_content.DropdownCustomerGrade, nil
}

func (s *dropdownService) GetSurveyTypeDropdown() ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	for _, surveyType := range model.MapSurveyType {
		dropdowns = append(dropdowns, surveyType)
	}
	return dropdowns, nil
}

func (s *dropdownService) GetSurveyCriteriaDropdown(surveyType string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto
	for _, surveyCriteria := range model.MapSurveyCriteria[surveyType] {
		dropdowns = append(dropdowns, surveyCriteria)
	}
	return dropdowns, nil
}

func (s *dropdownService) GetEditProfileDropdown() ([]model.DropdownDto, error) {
	return constants_content.DropdownEditProfileRequest, nil
}

func (s *dropdownService) GetProvinceDropdownByPostCode(postCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	provinceDropdownList, err := s.CityRepo.GetProvinceByPostCode(postCode)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	for _, item := range provinceDropdownList {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      item.Id,
			Value:   *item.CityCode,
			LabelTh: *item.DescriptionTh,
			LabelEn: *item.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetDistrictDropdownByPostCodeAndCityID(postCode *string, cityID int) ([]model.DropdownDto, error) {

	var dropdowns []model.DropdownDto

	districtDropdownList, err := s.DistrictRepo.GetMasterDistrictByPostCodeAndCityID(postCode, cityID)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	for _, item := range districtDropdownList {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      item.Id,
			Value:   *item.CityCode,
			LabelTh: *item.DescriptionTh,
			LabelEn: *item.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetSubDistrictDropdownByPostCodeAndDistrictID(postCode *string, districtID int) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	subDistrictDropdownList, err := s.SubDistrictRepo.GetMasterSubDistrictByPostCodeAndDistrictID(postCode, districtID)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	for _, item := range subDistrictDropdownList {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      item.Id,
			Value:   *item.CityCode,
			LabelTh: *item.DescriptionTh,
			LabelEn: *item.DescriptionEn,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetBuyerPurchaseDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.LotSettingRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name IN (?)", constant.ConfigParamConst.DROPDOWN_BUYER_PURCHASE_STATUS)

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAuctionDepositStatusDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.AuctionDepositStatusRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_AUCTION_DEPOSIT)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.AUCTION_DEPOSIT_STATUS)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetPaymentTypeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.PaymentTypeRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_PAYMENT)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.PAYMENT_TYPE)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRegisterTypeDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.RegisterTypeRepo.GetDB().Model(&entity.MasterRegisterType{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var registerTypes []entity.MasterRegisterType
	if err := query.Order("line_no asc").Find(&registerTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, registerType := range registerTypes {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      registerType.Id,
			Value:   util.Val(registerType.AttributeCode),
			LabelTh: util.Val(registerType.OptionTh),
			LabelEn: util.Val(registerType.OptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetRegisterTypeCarDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.RegisterTypeCarRepo.GetDB().Model(&entity.MasterRegisterTypeCar{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	var registerTypeCars []entity.MasterRegisterTypeCar
	if err := query.Order("asset_register_type_code asc").Find(&registerTypeCars).Error; err != nil {
		return dropdowns, err
	}

	for _, registerTypeCar := range registerTypeCars {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      registerTypeCar.Id,
			Value:   util.Val(registerTypeCar.AssetRegisterTypeCode),
			LabelTh: util.Val(registerTypeCar.DescriptionTh),
			LabelEn: util.Val(registerTypeCar.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetMasterPaymentMethodDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PaymentMethodRepo.GetDB().Model(&entity.MasterPaymentMethod{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	query = query.Order("payment_method_code asc")

	var masterPaymentMethods []entity.MasterPaymentMethod
	if err := query.Find(&masterPaymentMethods).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterPaymentMethods {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.PaymentMethodCode),
			LabelTh: util.Val(m.DescriptionTh),
			LabelEn: util.Val(m.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetMasterBankDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PaymentMethodRepo.GetDB().Model(&entity.MasterBank{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	query = query.Order("bank_account_code asc")

	var masterBanks []entity.MasterBank
	if err := query.Find(&masterBanks).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterBanks {

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.BankAccountCode),
			LabelTh: util.Val(m.DescriptionTh),
			LabelEn: util.Val(m.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetVendorDropdownWithFilter(c *fiber.Ctx) (model.DropdownDtoWithPaginate, error) {
	var res model.DropdownDtoWithPaginate
	res.Dropdowns = []model.DropdownDto{}
	var LabelTh string
	var LabelEn string
	isActive := c.Query("isActive", "")
	filter := c.Query("vendorInfo", "")
	page := c.Query("page", "")
	size := c.Query("size", "")
	selectedRecord := c.Query("selectedRecord", "")

	query := s.VendorRepo.GetDB().Model(&entity.MasterVendor{})

	if selectedRecord != "" {
		query = query.Select(`*, 
			CASE 
				WHEN id IN (` + selectedRecord + `) THEN 0 
				ELSE NULL 
			END AS custom_page`).Order("custom_page asc")
	}
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}

	if filter != "" {

		likeFilter := fmt.Sprintf("%%%s%%", filter)
		filterConditions := `
			vendor_no ILIKE ? 
			OR vendor_name ILIKE ? 
			OR COALESCE(NULLIF(vendor_group, ''), '-') ILIKE ? 
			OR CONCAT_WS(' ', vendor_name, COALESCE(NULLIF(vendor_group, ''), '-')) ILIKE ?
			OR CONCAT_WS(' ', vendor_no, vendor_name) ILIKE ?
			OR CONCAT_WS(' ', vendor_no, CONCAT_WS(' ', vendor_name, COALESCE(NULLIF(vendor_group, ''), '-'))) ILIKE ?
		`

		if selectedRecord != "" {
			query = query.Where(filterConditions+` OR id IN (`+selectedRecord+`)`, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter)
		} else {
			query = query.Where(filterConditions, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter, likeFilter)
		}
	}

	var totalRecord int64
	if err := query.Count(&totalRecord).Error; err != nil {
		return res, errs.NewError(http.StatusInternalServerError, err)
	}
	res.Total = totalRecord

	if page != "" {
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			log.Error("Invalid page number:", err)
			return res, err
		}
		sizeInt := constants_content.LimitFetchDropdown
		if size != "" {
			sizeInt, err = strconv.Atoi(size)
			if err != nil {
				log.Error("Invalid size:", err)
				return res, err
			}
		}
		limit := sizeInt
		offset := (pageInt - 1) * limit
		query = query.Offset(offset).Limit(limit)
	}

	var masterVendors []entity.MasterVendor
	if err := query.Order("vendor_no asc").Find(&masterVendors).Error; err != nil {
		return res, err
	}

	for _, m := range masterVendors {
		if m.VendorGroup == nil || util.Val(m.VendorGroup) == "" {
			LabelTh = util.Val(m.VendorNo) + " " + util.Val(m.VendorName) + " -"
		} else {
			LabelTh = util.Val(m.VendorNo) + " " + util.Val(m.VendorName) + " " + util.Val(m.VendorGroup)
		}
		LabelEn = LabelTh
		res.Dropdowns = append(res.Dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.VendorNo),
			LabelTh: util.Val(&LabelTh),
			LabelEn: util.Val(&LabelEn),
		})
	}

	return res, nil
}

// Brand
func (s *dropdownService) GetMasterBrandDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PaymentMethodRepo.GetDB().Model(&entity.MasterBrand{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	query = query.Order("brand_code asc")

	var masterBrands []entity.MasterBrand
	if err := query.Find(&masterBrands).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterBrands {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.BrandCode),
			LabelTh: util.Val(m.Description),
			LabelEn: util.Val(m.Description),
		})
	}

	return dropdowns, nil
}

// Model
func (s *dropdownService) GetMasterModelDropdown(isActive string, brandCode string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PaymentMethodRepo.GetDB().Model(&entity.MasterModel{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	if brandCode != "" {
		query = query.Where("brand_code = ?", brandCode)
	}
	query = query.Order("model_code asc")

	var masterModel []entity.MasterModel
	if err := query.Find(&masterModel).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterModel {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.ModelCode),
			LabelTh: util.Val(m.Description),
			LabelEn: util.Val(m.Description),
		})
	}

	return dropdowns, nil
}

// CarTypeCon
func (s *dropdownService) GetConfigCarTypeConDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.CAR_TYPE_CON)
	query = query.Order("value_int asc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

// Structure Grade
func (s *dropdownService) GetConfigStructureGradeDropdown() ([]model.ConfigParameterDropdownDto, error) {
	var dropdowns []model.ConfigParameterDropdownDto

	query := s.ConfigFeatureLotRepo.GetDB().Model(&entity.ConfigParameter{})
	query = query.Where("parameter_module = ?", constant.ConfigParamConst.MODULE_SYSTEM_ADMIN)
	query = query.Where("parameter_name = ?", constant.ConfigParamConst.STRUCTURE_GRADE)
	query = query.Order("value_int desc")

	var configParameters []entity.ConfigParameter
	if err := query.Find(&configParameters).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range configParameters {
		dropdowns = append(dropdowns, model.ConfigParameterDropdownDto{
			Id:            m.Id,
			ParameterName: m.ParameterName,
			LabelEn:       m.ValueString,
			LabelTh:       m.ValueString2,
			Value:         m.ValueInt,
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetAssetTypeDropdownInAuction(auctionId string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.AssetTypeRepo.GetDB().Model(&entity.AuctionAsset{}).
		Select("mat.id,mat.asset_type_code,mat.description_th,mat.description_en").
		Joins("JOIN master_asset_type mat on mat.id  = auction_asset.asset_type_id").
		Where("auction_asset.auction_id = ?", auctionId).
		Group("mat.id,mat.asset_type_code,mat.description_th,mat.description_en")

	var assetTypes []entity.MasterAssetType
	if err := query.Order("asset_type_code asc").Find(&assetTypes).Error; err != nil {
		return dropdowns, err
	}

	for _, assetType := range assetTypes {
		if assetType.AssetTypeCode == nil || assetType.DescriptionTh == nil || assetType.DescriptionEn == nil {
			continue
		}

		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      assetType.Id,
			Value:   *assetType.AssetTypeCode,
			LabelTh: *assetType.DescriptionTh,
			LabelEn: *assetType.DescriptionEn,
		})
	}

	return dropdowns, nil
}

// car paint
func (s *dropdownService) GetMasterCarPaintDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.PaymentMethodRepo.GetDB().Model(&entity.MasterCarPaint{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	query = query.Order("car_paint_code asc")

	var masterCarPaint []entity.MasterCarPaint
	if err := query.Find(&masterCarPaint).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterCarPaint {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.CarPaintCode),
			LabelTh: util.Val(m.DescriptionTh),
			LabelEn: util.Val(m.DescriptionEn),
		})
	}

	return dropdowns, nil
}

func (s *dropdownService) GetMasterVoidReasonDropdown(isActive string) ([]model.DropdownDto, error) {
	var dropdowns []model.DropdownDto

	query := s.ReasonRepo.GetDB().Model(&entity.MasterReason{})
	if isActive != "" {
		query = query.Where("is_active = ?", isActive)
	}
	query = query.Order("reason_code asc").Where("type = ?", "Void")

	var masterReason []entity.MasterReason
	if err := query.Find(&masterReason).Error; err != nil {
		return dropdowns, err
	}

	for _, m := range masterReason {
		dropdowns = append(dropdowns, model.DropdownDto{
			Id:      m.Id,
			Value:   util.Val(m.ReasonCode),
			LabelTh: util.Val(m.DescriptionTh),
			LabelEn: util.Val(m.DescriptionEn),
		})
	}

	return dropdowns, nil
}
