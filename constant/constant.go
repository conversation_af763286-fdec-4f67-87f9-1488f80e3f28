package constant

import (
	"backend-common-lib/constant"
	customer_grade "backend-common-lib/constants/customer_grade"
	editProfileApprovalStatus "backend-common-lib/constants/edit_profile_approval_status"
	rps "backend-common-lib/constants/registration_approval_status"
	"backend-common-lib/model"
)

var SortingDueNotiConfig = map[string][]string{
	"days_before_due_th":  {"cfg.value_string2"},
	"days_before_due_en":  {"cfg.value_string"},
	"customer_group_desc": {"customer_group.description_th"},
}

var SortingAdditionalService = map[string][]string{
	"service_type_th": {"cfg.value_string2"},
	"service_type_en": {"cfg.value_string"},
	"start_date":      {"start_date", "end_date"},
	"end_date":        {"start_date", "end_date"},
}

var SortingAdditionalServiceInterest = map[string][]string{
	"service_name": {"additional_service.service_name"},
	"buyer_name":   {"buyer.first_name", "buyer.middle_name", "buyer.last_name"},
	"bidder_id":    {"buyer.bidder_id"},
	"phone_number": {"buyer.phone_number"},
	"email":        {"buyer.email"},
}

var SortingPolicyConsent = map[string][]string{
	"version":       {"version_major", "version_minor"},
	"version_major": {"version_major", "version_minor"},
}

type DropdownItem struct {
	Value   string
	LabelTh string
	LabelEn string
}

var DropdownActionStatus = []DropdownItem{
	{Value: constant.ActionStatusWaitingEn, LabelTh: constant.ActionStatusWaitingTh, LabelEn: constant.ActionStatusWaitingEn},
	{Value: constant.ActionStatusApprovedEn, LabelTh: constant.ActionStatusApprovedTh, LabelEn: constant.ActionStatusApprovedEn},
	{Value: constant.ActionStatusRejectedEn, LabelTh: constant.ActionStatusRejectedTh, LabelEn: constant.ActionStatusRejectedEn},
}

const (
	PATH_PDPA                          = "pdpa"
	PATH_MARKETING                     = "marketing"
	PATH_PROXY_BID                     = "proxy-bid"
	PATH_WITHHOLDING_TAX               = "withholding-tax"
	PATH_SELLER_PDPA                   = "seller-pdpa"
	CONSENT_TYPE_BUYER_PDPA            = "BUYER_PDPA"
	CONSENT_TYPE_BUYER_MARKETING       = "BUYER_MARKETING"
	CONSENT_TYPE_BUYER_PROXY_BID       = "BUYER_PROXY_BID"
	CONSENT_TYPE_BUYER_WITHHOLDING_TAX = "BUYER_WITHHOLDING_TAX"
	CONSENT_TYPE_SELLER_PDPA           = "SELLER_PDPA"

	USER_TYPE_BUYER  = "BUYER"
	USER_TYPE_SELLER = "SELLER"
)

var DropdownAnswerType = []model.DropdownDto{
	{Value: constant.AnswerTypeSingleSelectValue, LabelTh: constant.AnswerTypeSingleSelectLabelTH, LabelEn: constant.AnswerTypeSingleSelectLabelEN},
	{Value: constant.AnswerTypeMultipleSelectValue, LabelTh: constant.AnswerTypeMultipleSelectLabelTH, LabelEn: constant.AnswerTypeMultipleSelectLabelEN},
	{Value: constant.AnswerTypeTextValue, LabelTh: constant.AnswerTypeTextLabelTH, LabelEn: constant.AnswerTypeTextLabelEN},
}

var DropdownRegistrationRequest = []model.DropdownDto{
	{Value: string(rps.RegistrationApprovalStatusApproved), LabelTh: constant.RegistrationApprovalStatusApprovedLabelTH, LabelEn: constant.RegistrationApprovalStatusApprovedLabelEN},
	{Value: string(rps.RegistrationApprovalStatusRejected), LabelTh: constant.RegistrationApprovalStatusRejectedLabelTH, LabelEn: constant.RegistrationApprovalStatusRejectedLabelEN},
	{Value: string(rps.RegistrationApprovalStatusWaiting), LabelTh: constant.RegistrationApprovalStatusWaitingLabelTH, LabelEn: constant.RegistrationApprovalStatusWaitingLabelEN},
}

var DropdownCustomerGrade = []model.DropdownDto{
	{Value: string(customer_grade.CustomerGradeA), LabelTh: constant.CustomerGradeATh, LabelEn: constant.CustomerGradeAEn},
	{Value: string(customer_grade.CustomerGradeB), LabelTh: constant.CustomerGradeBTh, LabelEn: constant.CustomerGradeBEn},
	{Value: string(customer_grade.CustomerGradeC), LabelTh: constant.CustomerGradeCTh, LabelEn: constant.CustomerGradeCEn},
	{Value: string(customer_grade.CustomerGradeD), LabelTh: constant.CustomerGradeDTh, LabelEn: constant.CustomerGradeDEn},
}
var DropdownCreditLimitRequest = []model.DropdownDto{
	{Value: string(rps.RegistrationApprovalStatusApproved), LabelTh: constant.RegistrationApprovalStatusApprovedLabelTH, LabelEn: constant.RegistrationApprovalStatusApprovedLabelEN},
	{Value: string(rps.RegistrationApprovalStatusRejected), LabelTh: constant.RegistrationApprovalStatusRejectedLabelTH, LabelEn: constant.RegistrationApprovalStatusRejectedLabelEN},
	{Value: string(rps.RegistrationApprovalStatusWaiting), LabelTh: constant.RegistrationApprovalStatusWaitingLabelTH, LabelEn: constant.RegistrationApprovalStatusWaitingLabelEN},
}

var DropdownEditProfileRequest = []model.DropdownDto{
	{Value: string(editProfileApprovalStatus.EditProfileApprovalStatusApproved), LabelTh: constant.EditProfileApprovalStatusApprovedLabelTH, LabelEn: constant.EditProfileApprovalStatusApprovedLabelEN},
	{Value: string(editProfileApprovalStatus.EditProfileApprovalStatusRejected), LabelTh: constant.EditProfileApprovalStatusRejectedLabelTH, LabelEn: constant.EditProfileApprovalStatusRejectedLabelEN},
	{Value: string(editProfileApprovalStatus.EditProfileApprovalStatusWaiting), LabelTh: constant.EditProfileApprovalStatusWaitingLabelTH, LabelEn: constant.EditProfileApprovalStatusWaitingLabelEN},
}

var LimitFetchDropdown = 100

const (
	AUCTION_TYPE_NORMAL  = 1
	AUCTION_TYPE_SPECIAL = 2
)

const (
	UPLOAD_ADDITIONAL_SERVICE = "ADDITIONAL_SERVICE"
)
